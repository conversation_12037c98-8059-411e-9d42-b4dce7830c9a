<!--
  页面名称：企业培训订单新增/编辑表单
  功能描述：新增/编辑企业培训订单，支持关联商机、关联线索自动填充，电子合同和纸质合同共用一个附件
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="drawerTitle"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="order-form"
    >
      <!-- 订单信息 -->
      <div class="form-section">
        <div class="section-title">订单信息</div>

        <!-- 关联商机 -->
        <el-form-item label="关联商机" prop="businessOpportunity">
          <el-select
            v-model="form.businessOpportunity"
            placeholder="请选择关联商机 (可选)"
            style="width: 100%"
            @change="handleBusinessOpportunityChange"
          >
            <el-option label="请选择关联商机 (可选)" value="" />
            <el-option label="ABC科技数字化转型项目" value="1" />
            <el-option label="XYZ制造精益生产项目" value="2" />
          </el-select>

          <!-- 商机信息展示 -->
          <div v-if="businessOpportunityInfo" class="info-box business-opportunity-info">
            <div class="info-header">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              <span>商机信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="label">商机描述:</span>
                <span class="value">{{ businessOpportunityInfo.description }}</span>
              </div>
              <div class="info-item">
                <span class="label">预估金额:</span>
                <span class="value">¥{{ businessOpportunityInfo.estimatedAmount }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ businessOpportunityInfo.createTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">商机状态:</span>
                <span class="value">{{ businessOpportunityInfo.status }}</span>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联商机可自动填充部分企业信息</div>
        </el-form-item>

        <!-- 关联线索 -->
        <el-form-item label="关联线索" prop="lead">
          <el-select
            v-model="form.lead"
            placeholder="请选择关联线索 (可选)"
            style="width: 100%"
            @change="handleLeadChange"
          >
            <el-option label="请选择关联线索 (可选)" value="" />
            <el-option label="陈经理 - ABC科技" value="1" />
            <el-option label="李经理 - XYZ制造" value="2" />
          </el-select>

          <!-- 线索信息展示 -->
          <div v-if="leadInfo" class="info-box lead-info">
            <div class="info-header">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              <span>线索信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="label">联系人:</span>
                <span class="value">{{ leadInfo.contactPerson }}</span>
              </div>
              <div class="info-item">
                <span class="label">联系电话:</span>
                <span class="value">{{ leadInfo.contactPhone }}</span>
              </div>
              <div class="info-item">
                <span class="label">线索来源:</span>
                <span class="value">{{ leadInfo.source }}</span>
              </div>
              <div class="info-item">
                <span class="label">线索描述:</span>
                <span class="value">{{ leadInfo.description }}</span>
              </div>
              <div class="info-item">
                <span class="label">线索状态:</span>
                <span class="value status-converted">{{ leadInfo.status }}</span>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联线索可自动填充企业信息</div>
        </el-form-item>

        <!-- 企业名称 -->
        <el-form-item label="企业名称 *" prop="companyName">
          <el-input
            v-model="form.companyName"
            placeholder="请输入企业名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 培训项目 -->
        <el-form-item label="培训项目 *" prop="trainingProject">
          <el-input
            v-model="form.trainingProject"
            placeholder="请输入培训项目"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 培训周期 -->
        <el-form-item label="培训周期 *" prop="trainingPeriod">
          <el-date-picker
            v-model="form.trainingPeriod"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <!-- 培训人数 -->
        <el-form-item label="培训人数 *" prop="traineeCount">
          <el-input-number
            v-model="form.traineeCount"
            :min="1"
            :max="1000"
            placeholder="请输入培训人数"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 订单金额 -->
        <el-form-item label="订单金额 *" prop="orderAmount">
          <el-input
            v-model.number="form.orderAmount"
            placeholder="请输入订单金额"
            type="number"
            min="0"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <!-- 支付状态 -->
        <el-form-item label="支付状态 *" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
            <el-option label="待支付" value="unpaid" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>

        <!-- 我方负责人 -->
        <el-form-item label="我方负责人 *" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入我方负责人" maxlength="50" />
        </el-form-item>

        <!-- 纸质合同 -->
        <el-form-item label="纸质合同" prop="contractFile">
          <div class="file-upload-container">
            <div class="file-upload-row">
              <el-upload
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
                :limit="1"
                :show-file-list="false"
              >
                <el-button type="primary">选择文件</el-button>
              </el-upload>
              <span class="file-status-text">
                {{ fileList.length > 0 ? fileList[0].name : '未选择任何文件' }}
              </span>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import type { EnterpriseTrainingOrder } from '@/api/OrderCenter/enterprise-training'

// Props
interface Props {
  visible: boolean
  editData?: EnterpriseTrainingOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const fileList = ref<any[]>([])

// 表单数据
const form = reactive({
  businessOpportunity: '',
  lead: '',
  companyName: '',
  trainingProject: '',
  trainingPeriod: [] as string[],
  traineeCount: 1,
  orderAmount: '',
  paymentStatus: 'unpaid',
  manager: '',
  contractFile: null as any
})

// 表单验证规则
const rules: FormRules = {
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '企业名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  trainingProject: [
    { required: true, message: '请输入培训项目', trigger: 'blur' },
    { min: 2, max: 100, message: '培训项目长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  trainingPeriod: [{ required: true, message: '请选择培训周期', trigger: 'change' }],
  traineeCount: [
    { required: true, message: '请输入培训人数', trigger: 'blur' },
    { type: 'number', min: 1, message: '培训人数必须大于0', trigger: 'blur' }
  ],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '订单金额必须大于0', trigger: 'blur' }
  ],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }],
  manager: [{ required: true, message: '请输入我方负责人', trigger: 'blur' }]
}

// 商机信息
const businessOpportunityInfo = ref<any>(null)

// 线索信息
const leadInfo = ref<any>(null)

// 计算属性
const drawerTitle = computed(() => {
  return props.editData ? '编辑企业培训订单' : '新建企业培训订单'
})

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        if (props.editData) {
          // 编辑模式，填充表单数据
          Object.assign(form, {
            businessOpportunity: props.editData.businessOpportunity || '',
            lead: props.editData.lead || '',
            companyName: props.editData.companyName || '',
            trainingProject: props.editData.trainingProject || '',
            trainingPeriod: props.editData.trainingPeriod
              ? Array.isArray(props.editData.trainingPeriod)
                ? props.editData.trainingPeriod
                : props.editData.trainingPeriod.split(' - ')
              : [],
            traineeCount: props.editData.traineeCount || 1,
            orderAmount: props.editData.orderAmount || '',
            paymentStatus: props.editData.paymentStatus || 'unpaid',
            manager: props.editData.manager || '',
            contractFile: props.editData.contractFile || null
          })

          // 加载关联信息
          if (props.editData.businessOpportunity) {
            handleBusinessOpportunityChange(props.editData.businessOpportunity)
          }
          if (props.editData.lead) {
            handleLeadChange(props.editData.lead)
          }
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      })
    }
  }
)

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    businessOpportunity: '',
    lead: '',
    companyName: '',
    trainingProject: '',
    trainingPeriod: [],
    traineeCount: 1,
    orderAmount: '',
    paymentStatus: 'unpaid',
    manager: '',
    contractFile: null
  })
  fileList.value = []
  businessOpportunityInfo.value = null
  leadInfo.value = null
  formRef.value?.clearValidate()
}

// 商机选择变化处理
const handleBusinessOpportunityChange = (value: string) => {
  if (value === '1') {
    businessOpportunityInfo.value = {
      description: '为XX企业集团提供员工技能提升培训服务,包括管理技能、专业技能等。',
      estimatedAmount: '150,000',
      createTime: '2024-06-08',
      status: '跟进中'
    }
    // 自动填充企业信息
    form.companyName = 'ABC科技有限公司'
    form.trainingProject = '数字化转型管理培训'
  } else if (value === '2') {
    businessOpportunityInfo.value = {
      description: '为XYZ制造集团提供精益生产管理培训服务',
      estimatedAmount: '180,000',
      createTime: '2024-06-10',
      status: '跟进中'
    }
    // 自动填充企业信息
    form.companyName = 'XYZ制造集团'
    form.trainingProject = '精益生产管理培训'
  } else {
    businessOpportunityInfo.value = null
  }
}

// 线索选择变化处理
const handleLeadChange = (value: string) => {
  if (value === '1') {
    leadInfo.value = {
      contactPerson: '陈经理',
      contactPhone: '135****7890',
      source: '企业咨询',
      description: '咨询员工培训服务',
      status: '已转化'
    }
    // 自动填充企业信息
    form.companyName = 'ABC科技有限公司'
  } else if (value === '2') {
    leadInfo.value = {
      contactPerson: '李经理',
      contactPhone: '138****5678',
      source: '企业咨询',
      description: '咨询员工培训服务',
      status: '已转化'
    }
    // 自动填充企业信息
    form.companyName = 'XYZ制造集团'
  } else {
    leadInfo.value = null
  }
}

// 文件上传处理
const handleFileChange = (file: any) => {
  form.contractFile = file.raw
  // 更新文件列表显示
  fileList.value = [file]
}

// 文件移除处理
const handleFileRemove = () => {
  form.contractFile = null
  fileList.value = []
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 准备提交数据
    const submitData: EnterpriseTrainingOrder = {
      ...form,
      orderAmount: Number(form.orderAmount),
      trainingPeriod: form.trainingPeriod.join(' - '),
      orderStatus: 'pending_approval' // 新增订单默认为待审批状态
    }

    if (props.editData?.id) {
      // 编辑模式 - 模拟更新
      console.log('编辑数据:', submitData)
      ElMessage.success('编辑成功')
    } else {
      // 新增模式 - 模拟创建
      console.log('新增数据:', submitData)
      ElMessage.success('新增成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error(props.editData ? '编辑失败' : '新增失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.order-form {
  padding: 0 20px;
}

.form-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.info-box {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
  width: 100%;
  box-sizing: border-box;

  .info-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    width: 100%;

    .info-icon {
      margin-right: 6px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  .info-content {
    width: 100%;

    .info-item {
      display: flex;
      margin-bottom: 4px;
      font-size: 13px;
      line-height: 1.4;
      width: 100%;

      .label {
        color: #606266;
        min-width: 70px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .value {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }

      .status-converted {
        color: #f56c6c;
        font-weight: 500;
      }
    }
  }

  &.business-opportunity-info {
    background: #f0f9ff;
    border-left-color: #409eff;
    color: #409eff;

    .info-header {
      color: #409eff;
    }
  }

  &.lead-info {
    background: #fdf4ff;
    border-left-color: #a855f7;
    color: #a855f7;

    .info-header {
      color: #a855f7;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.file-status {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.file-upload-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
  width: 100%;

  &:hover {
    border-color: #c0c4cc;
  }

  .file-upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .el-upload {
    flex-shrink: 0;
  }

  .file-status-text {
    font-size: 12px;
    color: #909399;
    flex: 1;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;

  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px #409eff inset;
  }
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}
</style>
