import request from '@/config/axios'

export interface EnterpriseTrainingOrder {
  id?: number
  orderNumber?: string
  businessOpportunity?: string
  lead?: string
  companyName: string
  contactPerson?: string
  contactPhone?: string
  companyAddress?: string
  trainingProject: string
  trainingType?: string
  traineeCount: number
  trainingPeriod: string[] | string
  trainingLocation?: string
  trainer?: string
  trainingDescription?: string
  orderAmount: number
  orderStatus: string
  paymentStatus: string
  manager?: string
  contractType?: string
  contractFile?: any
  remark?: string
  createTime?: string
  updateTime?: string
}

export interface EnterpriseTrainingOrderQuery {
  orderStatus?: string
  paymentStatus?: string
  keyword?: string
  page: number
  size: number
}

export interface EnterpriseTrainingOrderResponse {
  list: EnterpriseTrainingOrder[]
  total: number
}

/**
 * 获取企业培训订单列表
 */
export const getEnterpriseTrainingList = (params: EnterpriseTrainingOrderQuery) => {
  return request.get<EnterpriseTrainingOrderResponse>({
    url: '/order-center/enterprise-training/list',
    params
  })
}

/**
 * 获取企业培训订单详情
 */
export const getEnterpriseTrainingDetail = (id: number) => {
  return request.get<EnterpriseTrainingOrder>({
    url: `/order-center/enterprise-training/${id}`
  })
}

/**
 * 新增企业培训订单
 */
export const createEnterpriseTraining = (data: EnterpriseTrainingOrder) => {
  return request.post<EnterpriseTrainingOrder>({
    url: '/order-center/enterprise-training',
    data
  })
}

/**
 * 更新企业培训订单
 */
export const updateEnterpriseTraining = (id: number, data: EnterpriseTrainingOrder) => {
  return request.put<EnterpriseTrainingOrder>({
    url: `/order-center/enterprise-training/${id}`,
    data
  })
}

/**
 * 删除企业培训订单
 */
export const deleteEnterpriseTraining = (id: number) => {
  return request.delete({
    url: `/order-center/enterprise-training/${id}`
  })
}

/**
 * 获取企业培训订单统计信息
 */
export const getEnterpriseTrainingStats = () => {
  return request.get({
    url: '/order-center/enterprise-training/stats'
  })
}

/**
 * 获取操作日志
 */
export const getEnterpriseTrainingOptLog = (orderId: string) => {
  return request.get({
    url: `/order-center/enterprise-training/${orderId}/opt-log`
  })
}
