/**
 * 服务套餐管理 Mock 数据
 * 用于功能演示和开发测试
 */

// 服务套餐数据类型定义
export interface ServicePackage {
  id: number
  name: string
  category: string
  thumbnail: string
  carouselList: string[]
  price: number
  originalPrice?: number
  unit: string
  packageType: string
  taskSplitRule: string
  featureList: string[]
  serviceDescription: string
  serviceDetails: string
  serviceProcess: string
  purchaseNotice: string
  status: 'pending' | 'active' | 'deleted'
  auditStatus: 'auditing' | 'approved' | 'rejected'
  agencyId: string
  agencyName: string
  advanceBookingDays: number
  timeSelectionMode: string
  appointmentMode: string
  serviceStartTime: string
  addressSetting: string
  maxBookingDays: number
  cancellationPolicy: string
  // 长周期套餐字段
  servicePeriod?: string
  serviceFrequency?: string
  singleServiceDuration?: string
  serviceTime?: string[]
  restDaySetting?: string
  // 次数次卡套餐字段
  serviceCount?: string
  countSingleDuration?: string
  serviceInterval?: string
  validityPeriod?: string
  createTime: number
  updateTime: number
}

// 数据字典选项类型
export interface DictOption {
  label: string
  value: string
}

// Mock 图片 URL（使用占位图片服务）
const mockImages = {
  thumbnails: [
    'https://picsum.photos/160/160?random=1',
    'https://picsum.photos/160/160?random=2',
    'https://picsum.photos/160/160?random=3',
    'https://picsum.photos/160/160?random=4',
    'https://picsum.photos/160/160?random=5',
    'https://picsum.photos/160/160?random=6',
    'https://picsum.photos/160/160?random=7',
    'https://picsum.photos/160/160?random=8',
    'https://picsum.photos/160/160?random=9',
    'https://picsum.photos/160/160?random=10'
  ],
  carousels: [
    'https://picsum.photos/400/300?random=11',
    'https://picsum.photos/400/300?random=12',
    'https://picsum.photos/400/300?random=13',
    'https://picsum.photos/400/300?random=14',
    'https://picsum.photos/400/300?random=15'
  ]
}

// 数据字典选项 Mock 数据
export const mockDictOptions = {
  // 服务分类
  serviceTypeOptions: [
    { label: '家政服务', value: 'housekeeping' },
    { label: '月嫂服务', value: 'maternity' },
    { label: '育儿嫂', value: 'childcare' },
    { label: '护工服务', value: 'nursing' },
    { label: '保洁服务', value: 'cleaning' },
    { label: '收纳整理', value: 'organizing' },
    { label: '家电清洗', value: 'appliance_cleaning' },
    { label: '深度保洁', value: 'deep_cleaning' }
  ],

  // 价格单位
  priceUnitOptions: [
    { label: '次', value: '次' },
    { label: '天', value: '天' },
    { label: '月', value: '月' },
    { label: '年', value: '年' },
    { label: '小时', value: '小时' },
    { label: '套', value: '套' }
  ],

  // 套餐类型
  packageTypeOptions: [
    { label: '长周期套餐', value: 'long-term' },
    { label: '次数次卡套餐', value: 'count-card' }
  ],

  // 预约时间范围
  appointmentTimeRangeOptions: [
    { label: '1天', value: '1' },
    { label: '3天', value: '3' },
    { label: '7天', value: '7' },
    { label: '15天', value: '15' },
    { label: '30天', value: '30' }
  ],

  // 服务开始时间
  serviceStartTimeOptions: [
    { label: '3天内开始', value: 'within-3-days' },
    { label: '7天内开始', value: 'within-7-days' },
    { label: '15天内开始', value: 'within-15-days' },
    { label: '30天内开始', value: 'within-30-days' }
  ],

  // 时间选择模式
  timeSelectionModeOptions: [
    { label: '固定时间', value: 'fixed' },
    { label: '灵活时间', value: 'flexible' },
    { label: '预约制', value: 'appointment' }
  ],

  // 地址设置
  addressSettingOptions: [
    { label: '固定地址', value: 'fixed' },
    { label: '客户地址', value: 'customer' },
    { label: '灵活地址', value: 'flexible' }
  ],

  // 预约模式
  appointmentModeOptions: [
    { label: '开始日期预约', value: 'start-date' },
    { label: '一次性预约全部服务次数', value: 'all-at-once' },
    { label: '分次预约', value: 'step-by-step' }
  ],

  // 长周期套餐 - 服务周期
  servicePeriodOptions: [
    { label: '26天', value: '26天' },
    { label: '30天', value: '30天' },
    { label: '42天', value: '42天' },
    { label: '60天', value: '60天' },
    { label: '90天', value: '90天' },
    { label: '180天', value: '180天' }
  ],

  // 长周期套餐 - 服务频次
  serviceFrequencyOptions: [
    { label: '每日', value: '每日' },
    { label: '工作日', value: '工作日' },
    { label: '周末', value: '周末' }
  ],

  // 长周期套餐 - 单次服务时长
  singleServiceDurationOptions: [
    { label: '2小时', value: '2小时' },
    { label: '4小时', value: '4小时' },
    { label: '6小时', value: '6小时' },
    { label: '8小时', value: '8小时' },
    { label: '12小时', value: '12小时' },
    { label: '24小时', value: '24小时' }
  ],

  // 长周期套餐 - 服务时间
  serviceTimeOptions: [
    { label: '9:00-13:00', value: '9:00-13:00' },
    { label: '14:00-18:00', value: '14:00-18:00' },
    { label: '8:00-16:00', value: '8:00-16:00' },
    { label: '8:00-20:00', value: '8:00-20:00' }
  ],

  // 长周期套餐 - 休息日设置
  restDaySettingOptions: [
    { label: '无特殊休息日设置', value: '' },
    { label: '周日休息', value: '周日休息' },
    { label: '法定节假日休息', value: '法定节假日休息' },
    { label: '周日及法定节假日休息', value: '周日及法定节假日休息' }
  ],

  // 次数次卡套餐 - 服务次数
  serviceCountOptions: [
    { label: '1次', value: '1次' },
    { label: '3次', value: '3次' },
    { label: '5次', value: '5次' },
    { label: '8次', value: '8次' },
    { label: '10次', value: '10次' },
    { label: '12次', value: '12次' },
    { label: '15次', value: '15次' },
    { label: '20次', value: '20次' }
  ],

  // 次数次卡套餐 - 单次服务时长
  countSingleDurationOptions: [
    { label: '1小时', value: '1小时' },
    { label: '2小时', value: '2小时' },
    { label: '3小时', value: '3小时' },
    { label: '4小时', value: '4小时' },
    { label: '6小时', value: '6小时' },
    { label: '8小时', value: '8小时' }
  ],

  // 次数次卡套餐 - 服务间隔
  serviceIntervalOptions: [
    { label: '请选择服务间隔', value: '' },
    { label: '每周1次', value: '每周1次' },
    { label: '每月1次', value: '每月1次' }
  ],

  // 次数次卡套餐 - 有效期
  validityPeriodOptions: [
    { label: '30天', value: '30天' },
    { label: '90天', value: '90天' },
    { label: '180天', value: '180天' },
    { label: '365天', value: '365天' }
  ],

  // 机构选项
  agencyOptions: [
    { label: '总部机构', value: 'headquarters' },
    { label: '北京分公司', value: 'beijing' },
    { label: '上海分公司', value: 'shanghai' },
    { label: '广州分公司', value: 'guangzhou' },
    { label: '深圳分公司', value: 'shenzhen' },
    { label: '杭州分公司', value: 'hangzhou' },
    { label: '成都分公司', value: 'chengdu' },
    { label: '武汉分公司', value: 'wuhan' }
  ]
}

// 生成随机时间戳
const getRandomTimestamp = (daysAgo: number = 30) => {
  const now = Date.now()
  const randomDays = Math.floor(Math.random() * daysAgo)
  return now - randomDays * 24 * 60 * 60 * 1000
}

// 生成随机特色标签
const getRandomFeatures = () => {
  const allFeatures = [
    '24小时服务',
    '专业认证',
    '上门服务',
    '灵活预约',
    '品质保障',
    '经验丰富',
    '服务周到',
    '价格优惠',
    '快速响应',
    '安全可靠',
    '专业培训',
    '持证上岗',
    '保险保障',
    '售后服务',
    '客户好评'
  ]
  const count = Math.floor(Math.random() * 3) + 2 // 2-4个特色标签
  const shuffled = allFeatures.sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// Mock 服务套餐列表数据
export const mockServicePackages: ServicePackage[] = [
  // 待上架套餐
  {
    id: 1,
    name: '专业月嫂服务套餐',
    category: 'maternity',
    thumbnail: mockImages.thumbnails[0],
    carouselList: [mockImages.carousels[0], mockImages.carousels[1]],
    price: 8800,
    originalPrice: 9800,
    unit: '月',
    packageType: '长周期套餐',
    taskSplitRule: '按时间周期执行',
    featureList: getRandomFeatures(),
    serviceDescription:
      '专业月嫂提供产后护理服务，包括新生儿护理、产妇护理、营养配餐等全方位服务。',
    serviceDetails:
      '服务内容包括：新生儿日常护理、喂养指导、产妇身体恢复护理、营养餐制作、家庭卫生清洁等。',
    serviceProcess: '1. 预约咨询 2. 上门评估 3. 制定服务方案 4. 开始服务 5. 定期回访',
    purchaseNotice: '请提前7-15天预约，确保服务人员安排。服务期间如有特殊需求请及时沟通。',
    status: 'pending',
    auditStatus: 'auditing',
    agencyId: 'beijing',
    agencyName: '北京分公司',
    advanceBookingDays: 15,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-7-days',
    addressSetting: 'customer',
    maxBookingDays: 30,
    cancellationPolicy: '服务开始前24小时可免费取消',
    servicePeriod: '26天',
    serviceFrequency: '每日',
    singleServiceDuration: '24小时',
    serviceTime: ['8:00-20:00'],
    restDaySetting: '法定节假日休息',
    createTime: getRandomTimestamp(10),
    updateTime: getRandomTimestamp(5)
  },
  {
    id: 2,
    name: '深度保洁服务次卡',
    category: 'deep_cleaning',
    thumbnail: mockImages.thumbnails[1],
    carouselList: [mockImages.carousels[2], mockImages.carousels[3]],
    price: 299,
    unit: '次',
    packageType: '次数次卡套餐',
    taskSplitRule: '按次数执行',
    featureList: getRandomFeatures(),
    serviceDescription: '专业深度保洁服务，包括厨房、卫生间、客厅、卧室等全屋深度清洁。',
    serviceDetails: '服务内容：油烟机清洗、地板打蜡、玻璃清洁、家具除尘、卫生间消毒等。',
    serviceProcess: '1. 预约时间 2. 上门服务 3. 清洁作业 4. 验收确认 5. 服务评价',
    purchaseNotice: '请提前1-3天预约，服务时间约3-4小时。',
    status: 'pending',
    auditStatus: 'auditing',
    agencyId: 'shanghai',
    agencyName: '上海分公司',
    advanceBookingDays: 3,
    timeSelectionMode: 'appointment',
    appointmentMode: 'all-at-once',
    serviceStartTime: 'within-3-days',
    addressSetting: 'customer',
    maxBookingDays: 7,
    cancellationPolicy: '服务开始前2小时可免费取消',
    serviceCount: '5次',
    countSingleDuration: '4小时',
    serviceInterval: '每周1次',
    validityPeriod: '90天',
    createTime: getRandomTimestamp(8),
    updateTime: getRandomTimestamp(3)
  },
  {
    id: 3,
    name: '高级育儿嫂服务',
    category: 'childcare',
    thumbnail: mockImages.thumbnails[2],
    carouselList: [mockImages.carousels[4]],
    price: 6800,
    unit: '月',
    packageType: '长周期套餐',
    taskSplitRule: '按时间周期执行',
    featureList: getRandomFeatures(),
    serviceDescription: '专业育儿嫂提供0-3岁婴幼儿护理服务，包括日常护理、早教启蒙、营养配餐等。',
    serviceDetails: '服务包括：婴幼儿日常护理、辅食制作、早教游戏、生活习惯培养、安全看护等。',
    serviceProcess: '1. 需求沟通 2. 人员匹配 3. 试用期 4. 正式服务 5. 定期评估',
    purchaseNotice: '建议提前10-15天预约，可安排试用期以确保服务满意度。',
    status: 'pending',
    auditStatus: 'approved',
    agencyId: 'guangzhou',
    agencyName: '广州分公司',
    advanceBookingDays: 15,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-15-days',
    addressSetting: 'customer',
    maxBookingDays: 30,
    cancellationPolicy: '试用期内可随时调整，正式服务开始后需提前3天通知',
    servicePeriod: '30天',
    serviceFrequency: '工作日',
    singleServiceDuration: '8小时',
    serviceTime: ['8:00-16:00'],
    restDaySetting: '周日休息',
    createTime: getRandomTimestamp(12),
    updateTime: getRandomTimestamp(2)
  },
  {
    id: 4,
    name: '收纳整理服务包',
    category: 'organizing',
    thumbnail: mockImages.thumbnails[3],
    carouselList: [mockImages.carousels[0]],
    price: 199,
    unit: '次',
    packageType: '次数次卡套餐',
    taskSplitRule: '按次数执行',
    featureList: getRandomFeatures(),
    serviceDescription: '专业收纳师提供家庭空间整理服务，让您的家焕然一新。',
    serviceDetails: '服务包括：衣物整理、书籍分类、厨房收纳、储物空间优化等。',
    serviceProcess: '1. 现场评估 2. 制定方案 3. 整理收纳 4. 使用指导 5. 后续维护建议',
    purchaseNotice: '建议提前2-3天预约，单次服务时间约2-3小时。',
    status: 'pending',
    auditStatus: 'auditing',
    agencyId: 'shenzhen',
    agencyName: '深圳分公司',
    advanceBookingDays: 3,
    timeSelectionMode: 'appointment',
    appointmentMode: 'all-at-once',
    serviceStartTime: 'within-3-days',
    addressSetting: 'customer',
    maxBookingDays: 7,
    cancellationPolicy: '服务开始前4小时可免费取消',
    serviceCount: '3次',
    countSingleDuration: '3小时',
    serviceInterval: '每月1次',
    validityPeriod: '90天',
    createTime: getRandomTimestamp(6),
    updateTime: getRandomTimestamp(1)
  },
  // 已上架套餐
  {
    id: 5,
    name: '家电清洗专业服务',
    category: 'appliance_cleaning',
    thumbnail: mockImages.thumbnails[4],
    carouselList: [mockImages.carousels[1], mockImages.carousels[2]],
    price: 158,
    unit: '次',
    packageType: '次数次卡套餐',
    taskSplitRule: '按次数执行',
    featureList: getRandomFeatures(),
    serviceDescription: '专业家电清洗服务，包括空调、洗衣机、冰箱、油烟机等家电深度清洁。',
    serviceDetails: '服务范围：空调内外机清洗、洗衣机内筒清洁、冰箱除菌、油烟机深度清洗等。',
    serviceProcess: '1. 预约上门 2. 设备检查 3. 专业清洗 4. 功能测试 5. 清洁验收',
    purchaseNotice: '请提前1-2天预约，清洗过程约1-2小时，请确保家中有人配合。',
    status: 'active',
    auditStatus: 'approved',
    agencyId: 'hangzhou',
    agencyName: '杭州分公司',
    advanceBookingDays: 2,
    timeSelectionMode: 'appointment',
    appointmentMode: 'all-at-once',
    serviceStartTime: 'within-3-days',
    addressSetting: 'customer',
    maxBookingDays: 7,
    cancellationPolicy: '服务开始前2小时可免费取消',
    serviceCount: '1次',
    countSingleDuration: '2小时',
    serviceInterval: '每月1次',
    validityPeriod: '30天',
    createTime: getRandomTimestamp(20),
    updateTime: getRandomTimestamp(1)
  },
  {
    id: 6,
    name: '护工陪护服务套餐',
    category: 'nursing',
    thumbnail: mockImages.thumbnails[5],
    carouselList: [mockImages.carousels[3], mockImages.carousels[4]],
    price: 4800,
    unit: '月',
    packageType: '长周期套餐',
    taskSplitRule: '按时间周期执行',
    featureList: getRandomFeatures(),
    serviceDescription: '专业护工提供老人陪护服务，包括生活照料、医疗协助、心理陪伴等。',
    serviceDetails: '服务内容：日常生活照料、用药提醒、康复协助、心理疏导、紧急情况处理等。',
    serviceProcess: '1. 需求评估 2. 护工匹配 3. 服务培训 4. 开始陪护 5. 定期回访',
    purchaseNotice: '请提前5-7天预约，可根据老人具体情况定制服务方案。',
    status: 'active',
    auditStatus: 'approved',
    agencyId: 'chengdu',
    agencyName: '成都分公司',
    advanceBookingDays: 7,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-7-days',
    addressSetting: 'customer',
    maxBookingDays: 30,
    cancellationPolicy: '服务开始前48小时可免费取消',
    servicePeriod: '30天',
    serviceFrequency: '每日',
    singleServiceDuration: '12小时',
    serviceTime: ['8:00-20:00'],
    restDaySetting: '无特殊休息日设置',
    createTime: getRandomTimestamp(25),
    updateTime: getRandomTimestamp(3)
  },
  // 回收站套餐
  {
    id: 7,
    name: '临时保洁服务',
    category: 'cleaning',
    thumbnail: mockImages.thumbnails[6],
    carouselList: [mockImages.carousels[0]],
    price: 88,
    unit: '次',
    packageType: '次数次卡套餐',
    taskSplitRule: '按次数执行',
    featureList: getRandomFeatures(),
    serviceDescription: '临时保洁服务，适合突发清洁需求。',
    serviceDetails: '基础清洁服务，包括地面清洁、垃圾清理、简单整理等。',
    serviceProcess: '1. 快速预约 2. 上门服务 3. 清洁作业 4. 完成验收',
    purchaseNotice: '可当天预约，服务时间约1-2小时。',
    status: 'deleted',
    auditStatus: 'approved',
    agencyId: 'wuhan',
    agencyName: '武汉分公司',
    advanceBookingDays: 1,
    timeSelectionMode: 'flexible',
    appointmentMode: 'all-at-once',
    serviceStartTime: 'within-3-days',
    addressSetting: 'customer',
    maxBookingDays: 3,
    cancellationPolicy: '服务开始前1小时可免费取消',
    serviceCount: '1次',
    countSingleDuration: '2小时',
    serviceInterval: '每周1次',
    validityPeriod: '30天',
    createTime: getRandomTimestamp(30),
    updateTime: getRandomTimestamp(5)
  },
  {
    id: 8,
    name: '专业住家保姆服务套餐',
    category: 'housekeeping',
    thumbnail: mockImages.thumbnails[7],
    carouselList: [mockImages.carousels[1], mockImages.carousels[3]],
    price: 5200,
    originalPrice: 5800,
    unit: '月',
    packageType: '长周期套餐',
    taskSplitRule: '按时间周期执行',
    featureList: getRandomFeatures(),
    serviceDescription:
      '专业住家保姆提供全方位家庭服务，包括家务清洁、三餐制作、老人陪护、儿童看护等综合性服务。',
    serviceDetails:
      '服务内容包括：日常家务清洁、一日三餐制作、老人生活照料、儿童接送看护、家庭物品整理、简单家电维护等全方位家庭服务。',
    serviceProcess: '1. 家庭需求评估 2. 保姆技能匹配 3. 面试试用期 4. 签约入住服务 5. 定期服务回访',
    purchaseNotice:
      '请提前10-15天预约，可安排面试和试用期。住家保姆需要提供独立房间，包含基本生活设施。',
    status: 'pending',
    auditStatus: 'auditing',
    agencyId: 'hangzhou',
    agencyName: '杭州分公司',
    advanceBookingDays: 15,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-15-days',
    addressSetting: 'customer',
    maxBookingDays: 30,
    cancellationPolicy: '服务开始前7天可免费取消，3-7天内取消收取30%费用，3天内取消收取50%费用',
    servicePeriod: '30天',
    serviceFrequency: '每日',
    singleServiceDuration: '24小时',
    serviceTime: ['8:00-20:00'],
    restDaySetting: '周日及法定节假日休息',
    createTime: getRandomTimestamp(3),
    updateTime: getRandomTimestamp(1)
  },
  {
    id: 9,
    name: '专业产后康复护理套餐',
    category: 'nursing',
    thumbnail: mockImages.thumbnails[8],
    carouselList: [mockImages.carousels[2], mockImages.carousels[4]],
    price: 680,
    unit: '次',
    packageType: '次数次卡套餐',
    taskSplitRule: '按次数执行',
    featureList: getRandomFeatures(),
    serviceDescription:
      '专业产后康复护理服务，包括产后身体恢复指导、心理疏导、营养调理、康复训练等专业护理服务。',
    serviceDetails:
      '服务内容包括：产后身体评估、康复运动指导、营养膳食建议、心理健康疏导、母乳喂养指导、产后护理知识培训等。',
    serviceProcess:
      '1. 产后评估咨询 2. 制定康复计划 3. 专业护理服务 4. 康复效果跟踪 5. 后续指导建议',
    purchaseNotice:
      '请提前3-5天预约，建议产后2-6周内开始康复护理。每次服务时间约2小时，建议连续进行。',
    status: 'pending',
    auditStatus: 'pending',
    agencyId: 'chengdu',
    agencyName: '成都分公司',
    advanceBookingDays: 5,
    timeSelectionMode: 'appointment',
    appointmentMode: 'all-at-once',
    serviceStartTime: 'within-7-days',
    addressSetting: 'customer',
    maxBookingDays: 15,
    cancellationPolicy: '服务开始前24小时可免费取消，24小时内取消收取20%费用',
    serviceCount: '8次',
    countSingleDuration: '2小时',
    serviceInterval: '每周1次',
    validityPeriod: '90天',
    createTime: getRandomTimestamp(2),
    updateTime: getRandomTimestamp(1)
  }
]

// Mock API 响应类型
export interface MockApiResponse<T> {
  code: number
  message: string
  data: T
}

// 分页响应类型
export interface PaginationResponse<T> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

// Mock API 函数
export const mockApi = {
  // 获取服务套餐列表
  getServicePackageList: (params: {
    pageNo: number
    pageSize: number
    keyword?: string
    category?: string
    agency?: string
    status?: string
  }): Promise<MockApiResponse<PaginationResponse<ServicePackage>>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredPackages = [...mockServicePackages]

        // 状态筛选
        if (params.status) {
          filteredPackages = filteredPackages.filter((pkg) => pkg.status === params.status)
        }

        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase()
          filteredPackages = filteredPackages.filter(
            (pkg) =>
              pkg.name.toLowerCase().includes(keyword) ||
              pkg.serviceDescription.toLowerCase().includes(keyword)
          )
        }

        // 分类筛选
        if (params.category) {
          filteredPackages = filteredPackages.filter((pkg) => pkg.category === params.category)
        }

        // 机构筛选
        if (params.agency) {
          filteredPackages = filteredPackages.filter((pkg) => pkg.agencyId === params.agency)
        }

        // 分页
        const start = (params.pageNo - 1) * params.pageSize
        const end = start + params.pageSize
        const list = filteredPackages.slice(start, end)

        resolve({
          code: 200,
          message: 'success',
          data: {
            list,
            total: filteredPackages.length,
            pageNo: params.pageNo,
            pageSize: params.pageSize
          }
        })
      }, 300) // 模拟网络延迟
    })
  },

  // 获取服务套餐详情
  getServicePackageDetail: (id: number): Promise<MockApiResponse<ServicePackage>> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const packageDetail = mockServicePackages.find((pkg) => pkg.id === id)
        if (packageDetail) {
          resolve({
            code: 200,
            message: 'success',
            data: packageDetail
          })
        } else {
          reject({
            code: 404,
            message: '套餐不存在'
          })
        }
      }, 200)
    })
  },

  // 模拟图片上传
  uploadImage: (file: File): Promise<MockApiResponse<{ url: string }>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟上传成功，返回随机图片URL
        const randomIndex = Math.floor(Math.random() * mockImages.thumbnails.length)
        resolve({
          code: 200,
          message: '上传成功',
          data: {
            url: mockImages.thumbnails[randomIndex]
          }
        })
      }, 1000)
    })
  },

  // 模拟套餐上架
  shelfServicePackage: (id: number): Promise<MockApiResponse<null>> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockServicePackages.findIndex((pkg) => pkg.id === id)
        if (index !== -1) {
          mockServicePackages[index].status = 'active'
          mockServicePackages[index].updateTime = Date.now()

          resolve({
            code: 200,
            message: '上架成功',
            data: null
          })
        } else {
          reject({
            code: 404,
            message: '套餐不存在'
          })
        }
      }, 500)
    })
  },

  // 模拟套餐状态更新
  updateServicePackageStatus: (id: number, status: string): Promise<MockApiResponse<null>> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockServicePackages.findIndex((pkg) => pkg.id === id)
        if (index !== -1) {
          mockServicePackages[index].status = status as any
          mockServicePackages[index].updateTime = Date.now()

          resolve({
            code: 200,
            message: '状态更新成功',
            data: null
          })
        } else {
          reject({
            code: 404,
            message: '套餐不存在'
          })
        }
      }, 300)
    })
  }
}
