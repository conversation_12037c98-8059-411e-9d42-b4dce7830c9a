<!--
  页面名称：上传纸质合同弹窗
  功能描述：上传纸质合同附件，支持文件选择、合同信息填写等
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="上传纸质合同"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="paper-contract-container">
      <!-- 上传表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="合同附件" prop="contractFile" required>
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :limit="1"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            class="contract-upload"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="upload-tip">支持格式: PDF、Word、JPG、PNG, 文件大小不超过10MB</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="合同编号" prop="contractNumber" required>
          <el-input
            v-model="form.contractNumber"
            placeholder="请输入合同编号"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同名称" prop="contractName" required>
          <el-input v-model="form.contractName" placeholder="请输入合同名称" style="width: 100%" />
        </el-form-item>

        <el-form-item label="签署日期" prop="signDate" required>
          <el-date-picker
            v-model="form.signDate"
            type="date"
            placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同金额" prop="contractAmount" required>
          <el-input
            v-model="form.contractAmount"
            placeholder="请输入合同金额"
            style="width: 100%"
            type="number"
            min="0"
            step="0.01"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 文件上传状态显示 -->
      <div v-if="fileList.length > 0" class="file-status-section">
        <div class="file-status-header">
          <span class="status-title">文件上传状态</span>
        </div>
        <div class="file-status-items">
          <div v-for="(file, index) in fileList" :key="index" class="file-status-item">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
              <el-tag
                :type="
                  file.status === 'success'
                    ? 'success'
                    : file.status === 'fail'
                      ? 'danger'
                      : 'warning'
                "
                size="small"
              >
                {{
                  file.status === 'success'
                    ? '上传成功'
                    : file.status === 'fail'
                      ? '上传失败'
                      : '待上传'
                }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Document } from '@element-plus/icons-vue'
import type { UploadInstance, UploadFile } from 'element-plus'
import type { UniversityPracticeOrder } from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import request from '@/config/axios'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref()
const uploadRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  contractFile: null as File | null,
  contractNumber: '',
  contractName: '',
  signDate: '',
  contractAmount: ''
})

// 文件列表
const fileList = ref<UploadFile[]>([])

// 监听订单数据变化，回显现有合同信息
watch(
  () => props.orderData,
  (newVal) => {
    console.log('=== 订单数据变化监听触发 ===')
    console.log('新的订单数据:', newVal)

    if (newVal) {
      // 回显订单基本信息
      form.value.contractNumber = newVal.orderNo || ''
      form.value.contractName = newVal.projectName || ''
      form.value.signDate = newVal.createTime ? formatDate(newVal.createTime) : ''
      form.value.contractAmount = newVal.totalAmount?.toString() || ''

      console.log('表单数据已回显:', {
        contractNumber: form.value.contractNumber,
        contractName: form.value.contractName,
        signDate: form.value.signDate,
        contractAmount: form.value.contractAmount
      })

      // 如果有现有合同文件，也回显到文件列表
      if (newVal.contractFileUrl) {
        console.log('发现现有合同文件:', newVal.contractFileUrl)
        // 这里可以添加文件回显逻辑
      }
    } else {
      console.log('订单数据为空，清空表单')
      // 清空表单数据
      form.value.contractNumber = ''
      form.value.contractName = ''
      form.value.signDate = ''
      form.value.contractAmount = ''
    }
  },
  { immediate: true, deep: true }
)

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单校验规则
const rules = {
  contractNumber: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
  contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  contractAmount: [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ]
}

// 文件选择变化
const handleFileChange = async (file: UploadFile, fileList: UploadFile[]) => {
  console.log('=== 文件选择变化开始 ===')
  console.log('选择的文件:', file)
  console.log('文件列表:', fileList)
  console.log('文件原始数据:', file.raw)

  // 检查文件大小
  if (file.raw && file.raw.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    console.log('文件过大，已拒绝')
    // 从列表中移除文件
    const index = fileList.findIndex((f) => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
    return
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ]

  console.log('文件类型:', file.raw?.type)
  console.log('允许的类型:', allowedTypes)

  if (file.raw && !allowedTypes.includes(file.raw.type)) {
    ElMessage.error('不支持的文件格式，请选择 PDF、Word、JPG、PNG 格式的文件')
    console.log('文件格式不支持，已拒绝')
    // 从列表中移除文件
    const index = fileList.findIndex((f) => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
    return
  }

  // 更新文件列表 - 直接使用传入的fileList参数
  console.log('传入的文件列表:', fileList)
  console.log('当前fileList.value:', fileList.value)

  // 确保文件列表正确更新
  if (fileList && fileList.length > 0) {
    fileList.value = [...fileList]
    console.log('文件列表已更新:', fileList.value)
  }

  // 保存文件到表单数据
  if (file.raw) {
    form.value.contractFile = file.raw
    console.log('文件已保存到表单:', file.raw.name, file.raw.size)
    console.log('表单中的文件:', form.value.contractFile)
  } else {
    console.error('文件原始数据为空!')
  }

  console.log('=== 文件选择变化结束 ===')
}

// 文件移除
const handleFileRemove = (file: UploadFile, fileList: UploadFile[]) => {
  console.log('文件移除:', file, fileList)
  fileList.value = [...fileList]
  form.value.contractFile = null
}

// 上传文件到服务器
const uploadFile = async (file: File): Promise<string> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('directory', 'university-practice-contract')

  console.log('开始上传文件到 /infra/file/upload 接口:', file.name)

  const res = await request.postOriginal({
    url: '/infra/file/upload',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

  console.log('文件上传响应:', res)
  console.log('响应数据结构:', {
    hasRes: !!res,
    hasResData: !!res.data,
    code: res.data?.code,
    hasData: !!res.data?.data,
    dataType: typeof res.data?.data,
    dataValue: res.data?.data,
    msg: res.data?.msg
  })

  // 根据接口返回格式：{ "code": 0, "data": "https://...", "msg": "" }
  const fileUrl = res.data?.data || res.data

  console.log('解析后的文件URL:', fileUrl)

  if (fileUrl && typeof fileUrl === 'string' && /^https?:\/\//.test(fileUrl)) {
    console.log('文件上传成功，URL:', fileUrl)
    return fileUrl
  } else {
    console.error('文件上传失败，响应数据:', res)
    throw new Error('文件上传失败，请重试')
  }
}

// 提交表单
const handleSubmit = async () => {
  console.log('=== 提交表单开始 ===')
  console.log('当前文件列表:', fileList.value)
  console.log('表单中的文件:', form.value.contractFile)

  try {
    await formRef.value.validate()
    console.log('表单验证通过')

    // 直接使用表单中已保存的文件，不依赖fileList
    if (!form.value.contractFile) {
      console.error('文件验证失败: 表单中没有文件')
      ElMessage.warning('请选择合同附件')
      return
    }

    console.log('文件验证通过:', {
      fileName: form.value.contractFile.name,
      fileSize: form.value.contractFile.size,
      fileType: form.value.contractFile.type
    })

    console.log('文件验证通过，开始上传...')
    loading.value = true

    console.log('开始提交表单，先上传文件...')

    // 先上传文件到 /infra/file/upload 接口
    const fileUrl = await uploadFile(form.value.contractFile)
    console.log('文件上传完成，URL:', fileUrl)

    // 更新文件状态
    if (fileList.value.length > 0) {
      fileList.value[0].status = 'success'
      fileList.value[0].url = fileUrl
    }

    console.log('开始提交合同信息...')

    // 创建 FormData 提交合同信息（不包含文件，因为文件已经上传）
    const formData = new FormData()
    formData.append('orderId', props.orderData?.id?.toString() || '')
    formData.append('orderNo', props.orderData?.orderNo || '')
    formData.append('contractNumber', form.value.contractNumber)
    formData.append('contractName', form.value.contractName)
    formData.append('signDate', form.value.signDate)
    formData.append('contractAmount', form.value.contractAmount)
    formData.append('fileUrl', fileUrl) // 传递已上传的文件URL

    console.log('提交的合同信息:', {
      orderId: props.orderData?.id,
      orderNo: props.orderData?.orderNo,
      contractNumber: form.value.contractNumber,
      contractName: form.value.contractName,
      signDate: form.value.signDate,
      contractAmount: form.value.contractAmount,
      fileUrl: fileUrl
    })

    // 调用上传接口
    const result = await UniversityPracticeOrderApi.uploadPaperContract(formData)
    console.log('合同信息提交完成，结果:', result)

    ElMessage.success('纸质合同上传成功')
    emit('success', {
      fileUrl: result.fileUrl,
      fileName: result.fileName,
      fileSize: result.fileSize,
      contractNumber: form.value.contractNumber,
      contractName: form.value.contractName,
      signDate: form.value.signDate,
      contractAmount: form.value.contractAmount
    })
    handleCancel()
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试')

    // 更新文件状态为失败
    if (fileList.value.length > 0) {
      fileList.value[0].status = 'fail'
    }
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.value = {
    contractFile: null,
    contractNumber: '',
    contractName: '',
    signDate: '',
    contractAmount: ''
  }
  // 重置文件列表
  fileList.value = []
}
</script>

<style scoped lang="scss">
.paper-contract-container {
  .contract-upload {
    .upload-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 8px;
    }
  }

  .file-status-section {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .file-status-header {
      margin-bottom: 16px;

      .status-title {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }
    }

    .file-status-items {
      .file-status-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e4e7ed;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .file-icon {
            font-size: 16px;
            color: #909399;
          }

          .file-name {
            color: #303133;
            font-weight: 500;
            flex: 1;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-required .el-form-item__label:before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}
</style>
