<!--
  页面名称：服务套餐管理
  功能描述：管理就业服务相关的服务套餐，包括套餐列表、新增、编辑、删除等功能
-->
<template>
  <div class="service-package-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="fas fa-shopping-cart"></i> 服务套餐</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> + 添加新商品
      </el-button>
    </div>

    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" class="status-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="listed">
        <template #label>
          已上架
          <span class="tab-badge">{{ listedCount }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="pending">
        <template #label>
          待上架
          <span class="tab-badge">{{ pendingCount }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="recycled">
        <template #label>
          回收站
          <span class="tab-badge">{{ recycledCount }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item>
          <el-input
            v-model="filterForm.keyword"
            placeholder="输入商品名称或ID..."
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="商品分类:">
          <el-select
            v-model="filterForm.category"
            placeholder="全部分类"
            clearable
            style="width: 200px"
          >
            <el-option label="全部分类" value="" />
            <el-option
              v-for="item in serviceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属机构:">
          <el-select
            v-model="filterForm.agency"
            placeholder="全部机构"
            clearable
            style="width: 200px"
          >
            <el-option label="全部机构" value="" />
            <el-option
              v-for="item in agencyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 套餐列表表格 -->
    <div class="package-table">
      <el-table v-loading="loading" :data="packageList" border style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID/序号" width="80" />
        <el-table-column label="套餐主图" width="120">
          <template #default="{ row }">
            <el-image :src="row.thumbnail" style="width: 80px; height: 60px" fit="cover" />
          </template>
        </el-table-column>
        <el-table-column label="套餐名称" min-width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.name }}</div>
              <div style="font-size: 12px; color: #999">ID: {{ row.id }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column label="价格(元)" width="120">
          <template #default="{ row }">
            <span style="color: #e74c3c; font-weight: bold">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="套餐类型" width="150">
          <template #default="{ row }">
            <el-tag :type="getPackageTypeTag(row.packageType)" size="small">
              {{ getPackageTypeText(row.packageType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskSplitRule" label="任务拆分规则" min-width="200" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getAuditStatusTag(row.auditStatus)" size="small">
              {{ getAuditStatusText(row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="所属机构" width="150">
          <template #default="{ row }">
            <div>
              <div>{{ row.agencyName || '-' }}</div>
              <div style="font-size: 12px; color: #999">ID: {{ row.agencyId || '-' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合作伙伴" width="150">
          <template #default="{ row }">
            <div>
              <div>{{ row.partnerName || '-' }}</div>
              <div style="font-size: 12px; color: #999">ID: {{ row.partnerId || '-' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <!-- 已上架列表操作 -->
            <template v-if="activeTab === 'listed'">
              <el-button type="text" size="small" @click="handleToggleStatus(row)">下架</el-button>
              <el-button type="text" size="small" @click="handleViewLog(row)">查看日志</el-button>
            </template>

            <!-- 待上架列表操作 -->
            <template v-if="activeTab === 'pending'">
              <!-- 待审核状态的操作 -->
              <template v-if="row.auditStatus === 'pending'">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleToggleStatus(row)"
                  >上架</el-button
                >
                <el-button type="text" size="small" @click="handleViewLog(row)">日志</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </template>
              <!-- 审核中状态的操作 -->
              <template v-if="row.auditStatus === 'auditing'">
                <el-button type="text" size="small" @click="handleAudit(row)">审核</el-button>
                <el-button type="text" size="small" @click="handleRecall(row)">撤回</el-button>
                <el-button type="text" size="small" @click="handleViewLog(row)">日志</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </template>
              <!-- 已拒绝状态的操作 -->
              <template v-if="row.auditStatus === 'rejected'">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleToggleStatus(row)"
                  >上架</el-button
                >
                <el-button type="text" size="small" @click="handleViewLog(row)">日志</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </template>
            </template>

            <!-- 回收站列表操作 -->
            <template v-if="activeTab === 'recycled'">
              <el-button type="text" size="small" style="color: #67c23a" @click="handleRestore(row)"
                >启用</el-button
              >
              <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(row)"
                >删除</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <span class="total-count">共{{ pagination.total }}条</span>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑表单组件 -->
    <ServicePackageForm
      :visible="drawerVisible"
      :is-edit="isEdit"
      :package-id="currentId"
      :service-type-options="serviceTypeOptions"
      :price-unit-options="priceUnitOptions"
      :package-type-options="packageTypeOptions"
      :appointment-time-range-options="appointmentTimeRangeOptions"
      :service-start-time-options="serviceStartTimeOptions"
      :time-selection-mode-options="timeSelectionModeOptions"
      :address-setting-options="addressSettingOptions"
      :appointment-mode-options="appointmentModeOptions"
      :service-period-options="servicePeriodOptions"
      :service-frequency-options="serviceFrequencyOptions"
      :single-service-duration-options="singleServiceDurationOptions"
      :service-time-options="serviceTimeOptions"
      :rest-day-setting-options="restDaySettingOptions"
      :service-count-options="serviceCountOptions"
      :count-single-duration-options="countSingleDurationOptions"
      :service-interval-options="serviceIntervalOptions"
      :validity-period-options="validityPeriodOptions"
      @close="handleCloseDrawer"
      @success="handleFormSuccess"
    />

    <!-- 审核抽屉 -->
    <el-drawer
      v-model="auditDrawerVisible"
      :title="isViewLogMode ? '套餐日志' : '套餐审核'"
      direction="rtl"
      size="800px"
      :before-close="handleCloseAuditDrawer"
      class="audit-drawer"
    >
      <PackageAuditDetail
        v-if="auditDrawerVisible && currentId"
        :package-id="currentId"
        :is-view-log="isViewLogMode"
        @close="handleCloseAuditDrawer"
        @success="handleAuditSuccess"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PackageAuditDetail from './components/packageAuditDetail.vue'
import ServicePackageForm from './components/servicePackageForm.vue'
import {
  getServicePackageList,
  deleteServicePackage,
  moveServicePackageToRecycle,
  updateSingleServicePackageStatus,
  shelfServicePackage,
  withdrawServicePackage
} from '@/api/mall/employment/servicePackage'
// 导入数据字典接口
import { getDictDataPage } from '@/api/system/dict/dict.data'
import type { ServicePackage } from '@/api/mall/employment/servicePackage'

// 响应式数据
const loading = ref(false)
const drawerVisible = ref(false)
const auditDrawerVisible = ref(false)
const isEdit = ref(false)
const currentId = ref<number>(0)
const isViewLogMode = ref(false)
const activeTab = ref('listed')

// 数据字典选项
const serviceTypeOptions = ref<any[]>([])
const priceUnitOptions = ref<any[]>([])
const packageTypeOptions = ref<any[]>([])
const appointmentTimeRangeOptions = ref<any[]>([])
const serviceStartTimeOptions = ref<any[]>([])
const timeSelectionModeOptions = ref<any[]>([])
const addressSettingOptions = ref<any[]>([])
const appointmentModeOptions = ref<any[]>([])
// 长周期套餐任务拆分规则选项
const servicePeriodOptions = ref<any[]>([])
const serviceFrequencyOptions = ref<any[]>([])
const singleServiceDurationOptions = ref<any[]>([])
const serviceTimeOptions = ref<any[]>([])
const restDaySettingOptions = ref<any[]>([])
// 次数次卡套餐任务拆分规则选项
const serviceCountOptions = ref<any[]>([])
const countSingleDurationOptions = ref<any[]>([])
const serviceIntervalOptions = ref<any[]>([])
const validityPeriodOptions = ref<any[]>([])
// 机构选项
const agencyOptions = ref<any[]>([])

const filterForm = reactive({
  keyword: '',
  category: '',
  agency: ''
})

const packageList = ref<ServicePackage[]>([])
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算各状态的数量
const listedCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'active').length
})

const pendingCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'pending').length
})

const recycledCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'deleted').length
})

// 加载数据字典选项
const loadDictOptions = async () => {
  try {
    console.log('[ServicePackage] 开始加载数据字典选项')

    // 服务分类
    const serviceTypeRes = await getDictDataPage({
      dictType: 'service_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    serviceTypeOptions.value = (serviceTypeRes.data?.list || serviceTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 价格单位
    const priceUnitRes = await getDictDataPage({
      dictType: 'price_unit',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    priceUnitOptions.value = (priceUnitRes.data?.list || priceUnitRes.list || []).map(
      (item: any) => {
        const originalLabel = item.label
        const processedLabel = item.label.startsWith('/') ? item.label.substring(1) : item.label
        console.log('[ServicePackage] 价格单位选项处理:', originalLabel, '->', processedLabel)
        return {
          label: processedLabel,
          value: item.value
        }
      }
    )

    // 如果数据字典中没有数据，使用备用选项
    if (priceUnitOptions.value.length === 0) {
      console.log('[ServicePackage] 使用备用价格单位选项')
      priceUnitOptions.value = [
        { label: '次', value: '次' },
        { label: '天', value: '天' },
        { label: '月', value: '月' },
        { label: '年', value: '年' }
      ]
    }

    console.log('[ServicePackage] 最终价格单位选项:', priceUnitOptions.value)

    // 套餐类型
    const packageTypeRes = await getDictDataPage({
      dictType: 'package_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    packageTypeOptions.value = (packageTypeRes.data?.list || packageTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 如果数据字典中没有数据，使用备用选项
    if (packageTypeOptions.value.length === 0) {
      console.log('[ServicePackage] 使用备用套餐类型选项')
      packageTypeOptions.value = [
        { label: '长周期套餐', value: 'long-term' },
        { label: '次数次卡套餐', value: 'count-card' }
      ]
    }

    console.log('[ServicePackage] 最终套餐类型选项:', packageTypeOptions.value)

    // 预约时间范围
    const appointmentTimeRangeRes = await getDictDataPage({
      dictType: 'appointment_time_range',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    appointmentTimeRangeOptions.value = (
      appointmentTimeRangeRes.data?.list ||
      appointmentTimeRangeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 服务开始时间
    const serviceStartTimeRes = await getDictDataPage({
      dictType: 'service_start_time',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    serviceStartTimeOptions.value = (
      serviceStartTimeRes.data?.list ||
      serviceStartTimeRes.list ||
      []
    )
      .filter((item: any) => {
        // 过滤掉指定的选项
        const excludeLabels = ['指定日期开始', '客户协商确定']
        return !excludeLabels.includes(item.label)
      })
      .map((item: any) => ({
        label: item.label,
        value: item.value
      }))

    // 如果数据字典中没有数据，使用备用选项
    if (serviceStartTimeOptions.value.length === 0) {
      console.log('[ServicePackage] 使用备用服务开始时间选项')
      serviceStartTimeOptions.value = [
        { label: '3天内开始', value: 'within-3-days' },
        { label: '7天内开始', value: 'within-7-days' },
        { label: '15天内开始', value: 'within-15-days' }
      ]
    }

    console.log('[ServicePackage] 最终服务开始时间选项:', serviceStartTimeOptions.value)

    // 时间选择模式
    const timeSelectionModeRes = await getDictDataPage({
      dictType: 'time_selection_mode',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    timeSelectionModeOptions.value = (
      timeSelectionModeRes.data?.list ||
      timeSelectionModeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 地址设置
    const addressSettingRes = await getDictDataPage({
      dictType: 'address_setting',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    addressSettingOptions.value = (
      addressSettingRes.data?.list ||
      addressSettingRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 预约模式
    const appointmentModeRes = await getDictDataPage({
      dictType: 'appointment_mode',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    appointmentModeOptions.value = (
      appointmentModeRes.data?.list ||
      appointmentModeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 长周期套餐任务拆分规则选项 - 根据PRD文档创建静态选项
    servicePeriodOptions.value = [
      { label: '26天', value: '26天' },
      { label: '30天', value: '30天' },
      { label: '42天', value: '42天' },
      { label: '60天', value: '60天' },
      { label: '90天', value: '90天' },
      { label: '180天', value: '180天' }
    ]

    serviceFrequencyOptions.value = [
      { label: '每日', value: '每日' },
      { label: '工作日', value: '工作日' },
      { label: '周末', value: '周末' }
    ]

    singleServiceDurationOptions.value = [
      { label: '2小时', value: '2小时' },
      { label: '4小时', value: '4小时' },
      { label: '6小时', value: '6小时' },
      { label: '8小时', value: '8小时' },
      { label: '12小时', value: '12小时' },
      { label: '24小时', value: '24小时' }
    ]

    serviceTimeOptions.value = [
      { label: '9:00-13:00', value: '9:00-13:00' },
      { label: '14:00-18:00', value: '14:00-18:00' },
      { label: '8:00-16:00', value: '8:00-16:00' },
      { label: '8:00-20:00', value: '8:00-20:00' }
    ]

    restDaySettingOptions.value = [
      { label: '无特殊休息日设置', value: '' },
      { label: '周日休息', value: '周日休息' },
      { label: '法定节假日休息', value: '法定节假日休息' },
      { label: '周日及法定节假日休息', value: '周日及法定节假日休息' }
    ]

    // 次数次卡套餐任务拆分规则选项 - 根据PRD文档创建静态选项
    serviceCountOptions.value = [
      { label: '1次', value: '1次' },
      { label: '2次', value: '2次' },
      { label: '4次', value: '4次' },
      { label: '6次', value: '6次' },
      { label: '8次', value: '8次' },
      { label: '10次', value: '10次' },
      { label: '12次', value: '12次' }
    ]

    countSingleDurationOptions.value = [
      { label: '2小时', value: '2小时' },
      { label: '3小时', value: '3小时' },
      { label: '4小时', value: '4小时' },
      { label: '6小时', value: '6小时' },
      { label: '8小时', value: '8小时' }
    ]

    serviceIntervalOptions.value = [
      { label: '请选择服务间隔', value: '' },
      { label: '每周1次', value: '每周1次' },
      { label: '每月1次', value: '每月1次' }
    ]

    validityPeriodOptions.value = [
      { label: '30天', value: '30天' },
      { label: '90天', value: '90天' },
      { label: '180天', value: '180天' },
      { label: '365天', value: '365天' }
    ]

    // 商品状态 - 已移除，状态固定为待上架且不可编辑

    // 加载机构列表
    await loadAgencyOptions()
  } catch (error) {
    console.error('加载数据字典失败:', error)
  }
}

// 加载机构选项
const loadAgencyOptions = async () => {
  try {
    // 这里应该调用获取机构列表的API
    // 暂时使用模拟数据，实际项目中需要替换为真实的API调用
    console.log('[ServicePackage] 加载机构选项')

    // 模拟API调用 - 实际项目中需要替换为真实的机构API
    // const agencyRes = await getAgencyList({ status: 'active' })
    // agencyOptions.value = (agencyRes.data?.list || agencyRes.list || []).map((item: any) => ({
    //   label: item.name,
    //   value: item.id
    // }))

    // 临时使用静态数据
    agencyOptions.value = [
      { label: '总部机构', value: 'headquarters' },
      { label: '北京分公司', value: 'beijing' },
      { label: '上海分公司', value: 'shanghai' },
      { label: '广州分公司', value: 'guangzhou' },
      { label: '深圳分公司', value: 'shenzhen' }
    ]

    console.log('[ServicePackage] 机构选项加载完成:', agencyOptions.value)
  } catch (error) {
    console.error('加载机构选项失败:', error)
    // 加载失败时使用默认选项
    agencyOptions.value = [{ label: '总部机构', value: 'headquarters' }]
  }
}

// 格式化时间
const formatTime = (timestamp: number | string) => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp))
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取套餐列表
const getPackageList = async () => {
  loading.value = true
  try {
    // 将activeTab的值映射为正确的status值
    let status = activeTab.value
    if (activeTab.value === 'listed') {
      status = 'active'
    } else if (activeTab.value === 'recycled') {
      status = 'deleted'
    }
    // pending保持不变

    const params = {
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: filterForm.keyword,
      category: filterForm.category,
      agency: filterForm.agency,
      status: status
    }

    console.log('[ServicePackage] 获取套餐列表参数:', params)

    // 使用真实 API
    const response = await getServicePackageList(params)
    console.log('[ServicePackage] API响应:', response)

    // 根据实际返回的数据结构处理
    const responseData = response as any

    if (responseData && responseData.list) {
      // 直接返回的数据结构
      packageList.value = responseData.list || []
      pagination.total = responseData.total || 0
    } else if (responseData && responseData.data && responseData.data.list) {
      // 包装在ApiResponse中的数据结构
      packageList.value = responseData.data.list || []
      pagination.total = responseData.data.total || 0
    } else {
      packageList.value = []
      pagination.total = 0
    }

    console.log('[ServicePackage] 处理后的套餐列表:', packageList.value)
  } catch (error) {
    console.error('获取套餐列表失败:', error)
    ElMessage.error('获取套餐列表失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐类型标签
const getPackageTypeTag = (type: string) => {
  switch (type) {
    case 'count-card':
      return 'danger'
    case 'long-term':
      return 'primary'
    default:
      return 'info'
  }
}

// 获取套餐类型文本
const getPackageTypeText = (type: string) => {
  switch (type) {
    case 'count-card':
    case '次数次卡套餐':
      return '次数次卡套餐'
    case 'long-term':
    case '长周期套餐':
      return '长周期套餐'
    default:
      return type || '未知'
  }
}

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'pending':
      return 'warning'
    case 'deleted':
      return 'info'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '已上架'
    case 'pending':
      return '待上架'
    case 'deleted':
      return '回收站'
    default:
      return '未知'
  }
}

// 获取审核状态标签类型
const getAuditStatusTag = (status: string) => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'auditing':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审核',
    auditing: '审核中',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

// 查询
const handleSearch = () => {
  pagination.currentPage = 1
  getPackageList()
}

// 重置
const handleReset = () => {
  filterForm.keyword = ''
  filterForm.category = ''
  filterForm.agency = ''
  pagination.currentPage = 1
  getPackageList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getPackageList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getPackageList()
}

// 新增套餐
const handleAdd = () => {
  isEdit.value = false
  currentId.value = 0
  drawerVisible.value = true
}

// 编辑套餐
const handleEdit = (row: ServicePackage) => {
  isEdit.value = true
  currentId.value = row.id
  drawerVisible.value = true
}

// 切换状态
const handleToggleStatus = async (row: ServicePackage) => {
  try {
    let successMessage = '状态更新成功'

    if (row.status === 'active') {
      // 下架操作
      await updateSingleServicePackageStatus(row.id, 'pending')
      successMessage = '已下架'
      ElMessage.success(successMessage)
      getPackageList()
    } else if (row.status === 'pending') {
      // 上架操作 - 显示确认弹窗
      await ElMessageBox.confirm(
        `确定要将套餐<strong>"${row.name}"</strong>上架吗？<br/>上架后用户将可以看到并购买此套餐。`,
        '确认上架',
        {
          confirmButtonText: '确认上架',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )

      // 用户确认后执行上架操作
      await shelfServicePackage(row.id)
      successMessage = '已申请上架'
      ElMessage.success(successMessage)
      getPackageList()
    } else if (row.status === 'deleted') {
      // 回收站中的套餐启用时，状态更新为待上架
      await updateSingleServicePackageStatus(row.id, 'pending')
      successMessage = '已启用，套餐已移动到待上架'
      ElMessage.success(successMessage)
      getPackageList()
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error === 'cancel') {
      return
    }
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

// 查看日志
const handleViewLog = (row: ServicePackage) => {
  currentId.value = row.id
  isViewLogMode.value = true
  auditDrawerVisible.value = true
}

// 审核套餐
const handleAudit = async (row: ServicePackage) => {
  currentId.value = row.id
  isViewLogMode.value = false
  auditDrawerVisible.value = true
}

// 撤回套餐
const handleRecall = async (row: ServicePackage) => {
  try {
    await ElMessageBox.confirm(`确定要撤回套餐"${row.name}"吗？`, '确认撤回', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await withdrawServicePackage(row.id)
    ElMessage.success('撤回成功')
    getPackageList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
      ElMessage.error('撤回失败')
    }
  }
}

// 恢复套餐（从回收站启用）
const handleRestore = async (row: ServicePackage) => {
  try {
    await ElMessageBox.confirm(`确定要启用套餐"${row.name}"吗？`, '确认启用', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await shelfServicePackage(row.id)
    // 这里应该调用恢复API
    ElMessage.success('启用成功')
    getPackageList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('启用失败:', error)
      ElMessage.error('启用失败')
    }
  }
}

// 删除套餐
const handleDelete = async (row: ServicePackage) => {
  try {
    // 根据当前tab状态决定操作类型
    const isRecycled = activeTab.value === 'recycled'
    const confirmMessage = isRecycled
      ? `确定要永久删除套餐"${row.name}"吗？`
      : `确定要将套餐"${row.name}"移动到回收站吗？`
    const successMessage = isRecycled ? '删除成功' : '已移动到回收站'

    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (isRecycled) {
      // 回收站中的套餐执行永久删除
      await deleteServicePackage(row.id)
    } else {
      // 已上架和待上架中的套餐移动到回收站
      await moveServicePackageToRecycle(row.id)
    }

    ElMessage.success(successMessage)
    getPackageList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 关闭抽屉
const handleCloseDrawer = () => {
  drawerVisible.value = false
}

// 表单提交成功回调
const handleFormSuccess = () => {
  drawerVisible.value = false
  getPackageList()
}

// 关闭审核抽屉
const handleCloseAuditDrawer = () => {
  auditDrawerVisible.value = false
  currentId.value = 0
  isViewLogMode.value = false
}

// 审核成功回调
const handleAuditSuccess = () => {
  ElMessage.success('审核操作成功')
  auditDrawerVisible.value = false
  currentId.value = 0
  getPackageList() // 刷新列表
}

// 监听标签页变化
const handleTabChange = () => {
  pagination.currentPage = 1
  getPackageList()
}

// 生命周期
onMounted(() => {
  loadDictOptions()
  getPackageList()
})
</script>

<style scoped lang="scss">
.service-package-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    color: #343a40;
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      color: #3498db;
    }
  }
}

.status-tabs {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 20px;
    border-bottom: 1px solid #dee2e6;
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  :deep(.el-tabs__item) {
    position: relative;
    display: flex;
    align-items: center;

    .tab-badge {
      margin-left: 6px;
      background: #e9ecef;
      color: #343a40;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 11px;
      font-weight: 500;
      line-height: 1;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 16px;
      height: 16px;
    }
  }

  :deep(.el-tabs__item.is-active) {
    .tab-badge {
      background: #3498db;
      color: white;
    }
  }
}

.filter-section {
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  .el-form-item {
    margin-bottom: 0;
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }

  .el-form-item__label {
    min-width: 80px;
    text-align: right;
  }
}

.package-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  :deep(.el-table) {
    width: 100%;
  }

  :deep(.el-table th) {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
    font-size: 14px;
  }

  :deep(.el-table td) {
    color: #666;
    font-size: 14px;
  }

  :deep(.el-table .el-tag) {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  :deep(.el-table .el-image) {
    border-radius: 4px;
  }

  :deep(.el-table .el-button--text) {
    padding: 0;
    margin-right: 10px;
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;

  .total-count {
    font-size: 14px;
    color: #666;
  }

  :deep(.el-pagination) {
    .el-pagination__total {
      display: none;
    }
  }
}

.editor-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .editor {
    min-height: 200px;
    padding: 10px;
  }
}

// 审核抽屉样式
.audit-drawer {
  :deep(.el-drawer__wrapper) {
    z-index: 2000;
  }

  :deep(.el-drawer__container) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-drawer__header) {
    padding: 20px 24px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
    background: #fff;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .el-drawer__close-btn {
      font-size: 18px;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }

  :deep(.el-drawer__body) {
    padding: 0;
    height: calc(100vh - 120px);
    overflow-y: auto;
    background: #f5f7fa;
  }
}
</style>
