<!--
  组件名称：收款确认对话框
  功能描述：用于确认高校实践订单的收款信息
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '更新收款信息' : '确认收款'"
    width="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="collection-form">
      <el-form-item label="订单号">
        <el-input v-model="form.orderNo" disabled />
      </el-form-item>

      <el-form-item label="订单金额">
        <el-input v-model="form.totalAmount" disabled>
          <template #append>元</template>
        </el-input>
      </el-form-item>

      <el-form-item label="实际收款金额*" prop="collectionAmount">
        <el-input-number
          v-model="form.collectionAmount"
          :min="0"
          :max="form.totalAmount"
          :precision="2"
          style="width: 100%"
          placeholder="请输入实际收款金额"
        />
      </el-form-item>

      <el-form-item label="收款方式*" prop="collectionMethod">
        <el-select v-model="form.collectionMethod" placeholder="请选择收款方式" style="width: 100%">
          <el-option
            v-for="(text, value) in collectionMethodOptions"
            :key="value"
            :label="text"
            :value="value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="收款日期*" prop="collectionDate">
        <el-date-picker
          v-model="form.collectionDate"
          type="date"
          placeholder="请选择收款日期"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item label="收款备注">
        <el-input
          v-model="form.collectionRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入收款备注(可选)..."
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '确认收款' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  UniversityPracticeOrderApi,
  type UniversityPracticeOrder,
  type ConfirmCollectionParams,
  type UpdateCollectionParams,
  CollectionMethodEnum,
  CollectionMethodTextMap
} from '@/api/OrderCenter/UniversityPracticeCenter'

interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder
  isEdit?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 收款方式选项
const collectionMethodOptions = computed(() => CollectionMethodTextMap)

// 表单数据
const form = reactive({
  orderId: 0,
  orderNo: '',
  totalAmount: 0,
  collectionAmount: 0,
  collectionMethod: '',
  collectionDate: '',
  operatorName: '当前用户', // 自动设置为当前用户
  collectionRemark: ''
})

// 表单验证规则
const rules: FormRules = {
  collectionAmount: [
    { required: true, message: '请输入实际收款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '收款金额不能小于0', trigger: 'blur' }
  ],
  collectionMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  collectionDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }]
}

// 监听订单数据变化
watch(
  () => props.orderData,
  (newData) => {
    if (newData) {
      form.orderId = newData.id || 0
      form.orderNo = newData.orderNo || ''
      form.totalAmount = newData.totalAmount || 0
      form.collectionAmount = newData.collectionAmount || newData.totalAmount || 0
      form.collectionMethod = newData.collectionMethod || ''
      form.collectionDate = newData.collectionDate || ''
      form.operatorName = '当前用户' // 自动设置为当前用户
      form.collectionRemark = newData.collectionRemark || ''
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.orderData) {
      // 重置表单
      form.orderId = props.orderData.id || 0
      form.orderNo = props.orderData.orderNo || ''
      form.totalAmount = props.orderData.totalAmount || 0
      form.collectionAmount = props.orderData.collectionAmount || props.orderData.totalAmount || 0
      form.collectionMethod = props.orderData.collectionMethod || ''
      form.collectionDate = props.orderData.collectionDate || ''
      form.operatorName = '当前用户' // 自动设置为当前用户
      form.collectionRemark = props.orderData.collectionRemark || ''
    }
  }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    if (props.isEdit) {
      // 更新收款信息
      const updateParams: UpdateCollectionParams = {
        orderId: form.orderId,
        orderNo: form.orderNo,
        paymentStatus: 'paid',
        collectionAmount: form.collectionAmount,
        collectionMethod: form.collectionMethod,
        collectionDate: form.collectionDate,
        operatorName: form.operatorName,
        collectionRemark: form.collectionRemark
      }

      await UniversityPracticeOrderApi.updateCollection(updateParams)
      ElMessage.success('收款信息更新成功')
    } else {
      // 确认收款
      const confirmParams: ConfirmCollectionParams = {
        orderId: form.orderId,
        orderNo: form.orderNo,
        paymentStatus: 'paid',
        collectionAmount: form.collectionAmount,
        collectionMethod: form.collectionMethod,
        collectionDate: form.collectionDate,
        operatorName: form.operatorName,
        collectionRemark: form.collectionRemark
      }

      await UniversityPracticeOrderApi.confirmCollection(confirmParams)
      ElMessage.success('收款确认成功')
    }

    // 传递收款数据
    const collectionData = {
      collectionAmount: form.collectionAmount,
      collectionMethod: form.collectionMethod,
      collectionDate: form.collectionDate,
      operatorName: form.operatorName,
      collectionRemark: form.collectionRemark
    }

    emit('success', collectionData)
    handleClose()
  } catch (error) {
    console.error('收款操作失败:', error)
    ElMessage.error(props.isEdit ? '收款信息更新失败' : '收款确认失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.collection-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number .el-input__wrapper) {
  padding-left: 11px;
}
</style>
