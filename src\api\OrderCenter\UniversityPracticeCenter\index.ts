import request from '@/config/axios'

// 高校实践订单相关接口类型定义
export interface UniversityPracticeOrder {
  id?: number
  orderNo?: string
  orderType?: string
  businessLine?: string
  // 下沉字段：商机和线索关联
  opportunityId?: number
  leadId?: number
  opportunityName?: string
  leadName?: string
  projectName: string
  projectDescription?: string
  startDate: string
  endDate: string
  totalAmount: number
  paidAmount?: number
  refundAmount?: number
  paymentStatus?: string
  orderStatus?: string
  managerId: number
  managerName: string
  managerPhone?: string
  contractType?: string
  contractFileUrl?: string
  contractStatus?: string
  remark?: string
  settlementStatus?: string
  settlementTime?: string
  settlementMethod?: string
  createTime?: string
  updateTime?: string
  // 高校实践订单特有字段
  universityName: string
  universityContact?: string
  universityPhone?: string
  universityEmail?: string
  enterpriseName: string
  enterpriseContact?: string
  enterprisePhone?: string
  enterpriseEmail?: string
  studentCount?: number
  practiceDuration?: string
  practiceLocation?: string
  serviceFee?: number
  managementFee?: number
  otherFee?: number
  // 支付相关字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 新增订单请求参数
export interface AddOrderParams {
  // 下沉字段：商机和线索关联
  opportunityId?: number
  leadId?: number
  opportunityName?: string
  leadName?: string
  projectName: string
  universityName: string
  enterpriseName: string
  startDate: string
  endDate: string
  totalAmount: number
  managerId: number
  managerName: string
  contractType?: string
  contractFileUrl?: string
  paymentStatus?: string
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 新增订单响应结果
export interface AddOrderResult {
  id: number
  orderNo: string
  createTime: string
}

// 更新订单请求参数
export interface UpdateOrderParams {
  id: number
  orderNo: string
  // 下沉字段：商机和线索关联
  opportunityId?: number
  leadId?: number
  opportunityName?: string
  leadName?: string
  projectName?: string
  universityName?: string
  enterpriseName?: string
  startDate?: string
  endDate?: string
  totalAmount?: number
  managerId?: number
  managerName?: string
  contractType?: string
  contractFileUrl?: string
  paymentStatus?: string
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 分页查询参数
export interface OrderPageParams {
  page: number
  size: number
  orderStatus?: string
  paymentStatus?: string
  keyword?: string
  startDate?: string
  endDate?: string
  managerId?: number
}

// 分页查询结果
export interface OrderPageResult {
  list: UniversityPracticeOrder
  total: number
  page: number
  size: number
}

// 订单统计概览结果
export interface OrderStatisticsResult {
  totalOrders: number
  monthlyNewOrders: number
  totalAmount: number
  monthlyNewAmount: number
  completedOrders: number
  inProgressOrders: number
  pendingApprovalOrders: number
  statusCounts: Array<{
    status: string
    statusName: string
    count: number
    percentage: number
  }>
  monthlyTrends: Array<{
    month: string
    orderCount: number
    orderAmount: number
  }>
  statisticsTime: string
}

// 合同上传参数
export interface UploadContractParams {
  orderId: number
  orderNo: string
  contractType: string
  file: File
  remark?: string
}

// 合同上传结果
export interface UploadContractResult {
  success: boolean
  fileUrl: string
  fileName: string
  fileSize: number
}

// 纸质合同上传结果
export interface UploadPaperContractResult {
  success: boolean
  fileUrl: string
  fileName: string
  fileSize: number
  contractNumber: string
  contractName: string
  signDate: string
  contractAmount: number
}

// 审批发起参数
export interface InitiateApprovalParams {
  orderId: number
  orderNo: string
  approvalType: string
  priority?: string
  approverIds: number[]
  comments?: string
}

// 审批发起结果
export interface InitiateApprovalResult {
  success: boolean
  approvalId: string
  approvalNo: string
}

// 审批记录项
export interface ApprovalRecordItem {
  id: number
  approvalId: string
  approvalNo: string
  approvalType: string
  priority: string
  status: string
  operatorId: number
  operatorName: string
  operatorRole: string
  action: string
  comments: string
  createTime: string
  updateTime: string
}

// 审批记录查询参数
export interface ApprovalRecordParams {
  orderId: number
  orderNo: string
  approvalType?: string
  status?: string
  startDate?: string
  endDate?: string
  page?: number
  size?: number
}

// 审批记录结果
export interface ApprovalRecordResult {
  list: ApprovalRecordItem[]
  total: number
}

// 操作日志查询参数
export interface OperationLogParams {
  orderId: number
  orderNo: string
  logType?: string
  startDate?: string
  endDate?: string
  page?: number
  size?: number
}

// 操作日志项
export interface OperationLogItem {
  id: number
  logType: string
  logTitle: string
  logContent: string
  oldStatus?: string
  newStatus?: string
  operatorId: number
  operatorName: string
  operatorRole: string
  relatedPartyType?: string
  relatedPartyName?: string
  createTime: string
}

// 操作日志结果
export interface OperationLogResult {
  list: OperationLogItem[]
  total: number
}

// 导出参数
export interface ExportParams {
  projectName?: string // 项目名称（模糊查询）
  universityName?: string // 高校名称（模糊查询）
  enterpriseName?: string // 企业名称（模糊查询）
  projectManager?: string // 项目经理（模糊查询）
  orderStatus?: string // 订单状态
  paymentStatus?: string // 支付状态
  startDate?: string // 开始日期
  endDate?: string // 结束日期
  exportFormat?: string // 导出格式
}

// 删除订单参数
export interface DeleteOrderParams {
  id: number
  orderNo: string
}

// 删除订单结果
export interface DeleteOrderResult {
  success: boolean
}

// 更新订单结果
export interface UpdateOrderResult {
  success: boolean
  updateTime: string
}

// 订单支付记录项
export interface OrderPaymentItem {
  id: number
  orderId: number
  orderNo: string
  paymentNo: string
  paymentType: string
  paymentAmount: number
  paymentStatus: string
  paymentTime: string
  operatorId: number
  operatorName: string
  paymentRemark?: string
  transactionId?: string
  createTime: string
}

// 创建支付记录参数
export interface CreatePaymentParams {
  orderId: number
  orderNo: string
  paymentType: string
  paymentAmount: number
  paymentTime: string
  operatorId: number
  operatorName: string
  paymentRemark?: string
}

// 支付记录查询参数
export interface PaymentQueryParams {
  orderId: number
  orderNo: string
  page?: number
  size?: number
}

// 支付记录查询结果
export interface PaymentQueryResult {
  list: OrderPaymentItem[]
  total: number
}

// 收款方式枚举
export enum CollectionMethodEnum {
  CASH = 'cash',
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  BANK_TRANSFER = 'bank_transfer',
  POS = 'pos',
  OTHER = 'other'
}

// 收款方式中文名称映射
export const CollectionMethodTextMap: Record<string, string> = {
  [CollectionMethodEnum.CASH]: '现金',
  [CollectionMethodEnum.WECHAT]: '微信支付',
  [CollectionMethodEnum.ALIPAY]: '支付宝',
  [CollectionMethodEnum.BANK_TRANSFER]: '银行转账',
  [CollectionMethodEnum.POS]: 'POS机刷卡',
  [CollectionMethodEnum.OTHER]: '其他'
}

// 确认收款请求参数
export interface ConfirmCollectionParams {
  orderId: number
  orderNo: string
  paymentStatus: string
  collectionAmount: number
  collectionMethod: string
  collectionDate: string
  operatorName: string
  collectionRemark?: string
}

// 确认收款响应结果
export interface ConfirmCollectionResult {
  success: boolean
  orderStatus: string
  paymentStatus: string
  updateTime: string
}

// 更新收款信息请求参数
export interface UpdateCollectionParams {
  orderId: number
  orderNo: string
  paymentStatus: string
  collectionAmount: number
  collectionMethod: string
  collectionDate: string
  operatorName: string
  collectionRemark?: string
}

// 更新收款信息响应结果
export interface UpdateCollectionResult {
  success: boolean
  updateTime: string
}

// 收款信息查询结果
export interface CollectionInfoResult {
  orderId: number
  orderNo: string
  paymentStatus: string
  collectionAmount: number
  collectionMethod: string
  collectionMethodText: string
  collectionDate: string
  operatorName: string
  collectionRemark?: string
  updateTime: string
}

// ==================== 下拉数据接口类型定义 ====================

// 获取订单下拉数据请求参数
export interface GetDropdownDataParams {
  orderType: string
  businessLine?: string
}

// 商机选项
export interface BusinessOption {
  id: number
  name: string
  customerName: string
  businessType: string
  totalPrice: number
  businessStage: string
  ownerUserName: string
}

// 线索选项
export interface LeadOption {
  id: number
  leadId: string
  customerName: string
  customerPhone: string
  businessModule: string
  leadSource: string
  leadStatus: string
}

// 客户选项
export interface CustomerOption {
  id: number
  customerName: string
  customerPhone: string
}

// 负责人选项
export interface ManagerOption {
  id: number
  managerName: string
  managerPhone: string
}

// 下拉数据响应结果
export interface DropdownDataResult {
  businessOptions: BusinessOption[]
  leadOptions: LeadOption[]
  customerOptions: CustomerOption[]
  managerOptions: ManagerOption[]
}

// 附件信息
export interface OrderAttachment {
  id?: number
  orderId: number
  orderNo: string
  fileName: string
  fileUrl: string
  filePath: string
  fileSize: number
  fileType: string
  attachmentType: string // 附件类型：contract(合同), invoice(发票), other(其他)
  remark?: string
  uploadTime: string
  uploaderId: number
  uploaderName: string
}

// 上传附件请求参数
export interface UploadAttachmentParams {
  orderId: number
  orderNo: string
  file: File
  attachmentType: string
  remark?: string
}

// 上传附件响应结果
export interface UploadAttachmentResult {
  success: boolean
  attachment: OrderAttachment
  message?: string
}

// 删除附件请求参数
export interface DeleteAttachmentParams {
  id: number
  orderId: number
  orderNo: string
}

// 查询附件列表参数
export interface QueryAttachmentParams {
  orderId: number
  orderNo: string
  attachmentType?: string
}

// 查询附件列表结果
export interface QueryAttachmentResult {
  list: OrderAttachment[]
  total: number
}

export const UniversityPracticeOrderApi = {
  // ==================== 基础CRUD接口 ====================

  // 分页查询高校实践订单
  getOrderPage: async (params: OrderPageParams): Promise<OrderPageResult> => {
    return await request.get({ url: '/publicbiz/order/page', params })
  },

  // 创建高校实践订单
  createOrder: async (data: AddOrderParams): Promise<AddOrderResult> => {
    return await request.post({ url: '/publicbiz/order/create', data })
  },

  // 更新高校实践订单
  updateOrder: async (data: UpdateOrderParams): Promise<UpdateOrderResult> => {
    return await request.post({ url: '/publicbiz/order/update', data })
  },

  // 删除高校实践订单
  deleteOrder: async (data: DeleteOrderParams): Promise<DeleteOrderResult> => {
    return await request.post({ url: '/publicbiz/order/delete', data })
  },

  // 查看高校实践订单详情
  getOrderDetail: async (params: {
    id: number
    orderNo: string
  }): Promise<UniversityPracticeOrder> => {
    return await request.post({ url: '/publicbiz/order/detail', data: params })
  },

  // 根据订单号查询高校实践订单
  getOrderByOrderNo: async (orderNo: string): Promise<UniversityPracticeOrder> => {
    return await request.get({ url: '/publicbiz/order/get-by-order-no', params: { orderNo } })
  },

  // ==================== 统计接口 ====================

  // 获取订单统计概览
  getOrderStatistics: async (): Promise<OrderStatisticsResult> => {
    return await request.get({ url: '/publicbiz/order/statistics/overview' })
  },

  // ==================== 合同管理接口 ====================

  // 上传合同附件
  uploadContract: async (data: FormData): Promise<UploadContractResult> => {
    return await request.post({
      url: '/publicbiz/order/upload-contract',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 下载合同附件
  downloadContract: async (params: {
    orderId: number
    orderNo: string
    fileUrl: string
  }): Promise<Blob> => {
    return await request.get({
      url: '/publicbiz/order/download-contract',
      params,
      responseType: 'blob'
    })
  },

  // 上传纸质合同
  uploadPaperContract: async (data: FormData): Promise<UploadPaperContractResult> => {
    return await request.post({
      url: '/publicbiz/order/upload-paper-contract',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // ==================== 审批流程接口 ====================

  // 发起审批
  initiateApproval: async (data: InitiateApprovalParams): Promise<InitiateApprovalResult> => {
    return await request.post({ url: '/publicbiz/order/initiate-approval', data })
  },

  // 获取审批记录
  getApprovalRecords: async (params: ApprovalRecordParams): Promise<ApprovalRecordResult> => {
    return await request.get({ url: '/publicbiz/order/approval-records', params })
  },

  // 审批通过
  approveOrder: async (data: {
    orderId: number
    orderNo: string
    approvalType: string
    comments?: string
  }): Promise<boolean | { code: number; data: boolean; msg: string }> => {
    return await request.post({
      url: '/publicbiz/order/approve',
      params: data
    })
  },

  // 审批驳回
  rejectOrder: async (data: {
    orderId: number
    orderNo: string
    approvalType: string
    rejectReason: string
    comments?: string
  }): Promise<boolean | { code: number; data: boolean; msg: string }> => {
    return await request.post({
      url: '/publicbiz/order/reject',
      params: data
    })
  },

  // ==================== 操作日志接口 ====================

  // 查询操作日志
  getOperationLogs: async (params: OperationLogParams): Promise<OperationLogResult> => {
    return await request.get({ url: '/publicbiz/order/operation-logs', params })
  },

  // ==================== 数据导出接口 ====================

  // 导出高校实践订单
  exportOrders: async (data: ExportParams): Promise<Blob> => {
    return await request.get({
      url: '/publicbiz/order/export-excel',
      params: data,
      responseType: 'blob'
    })
  },

  // ==================== 支付管理接口 ====================

  // 查询订单支付记录
  getOrderPayments: async (params: PaymentQueryParams): Promise<PaymentQueryResult> => {
    return await request.get({ url: '/publicbiz/order/payments', params })
  },

  // 创建支付记录
  createPayment: async (
    data: CreatePaymentParams
  ): Promise<{ success: boolean; paymentId: number }> => {
    return await request.post({ url: '/publicbiz/order/payment/create', data })
  },

  // ==================== 收款管理接口 ====================

  // 确认收款
  confirmCollection: async (data: ConfirmCollectionParams): Promise<ConfirmCollectionResult> => {
    return await request.post({ url: '/publicbiz/order/confirm-collection', data })
  },

  // 更新收款信息
  updateCollection: async (data: UpdateCollectionParams): Promise<UpdateCollectionResult> => {
    return await request.post({ url: '/publicbiz/order/update-collection', data })
  },

  // 查询收款信息
  getCollectionInfo: async (params: { id: number }): Promise<CollectionInfoResult> => {
    return await request.get({ url: '/publicbiz/order/collection-info', params })
  },

  // ==================== 附件管理接口 ====================

  // 上传附件
  uploadAttachment: async (data: FormData): Promise<UploadAttachmentResult> => {
    return await request.post({
      url: '/publicbiz/order/upload-attachment',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 删除附件
  deleteAttachment: async (data: DeleteAttachmentParams): Promise<{ success: boolean }> => {
    return await request.post({ url: '/publicbiz/order/delete-attachment', data })
  },

  // 查询附件列表
  getAttachmentList: async (params: QueryAttachmentParams): Promise<QueryAttachmentResult> => {
    return await request.get({ url: '/publicbiz/order/attachment-list', params })
  },

  // 下载附件
  downloadAttachment: async (params: {
    id: number
    orderId: number
    orderNo: string
  }): Promise<Blob> => {
    return await request.get({
      url: '/publicbiz/order/download-attachment',
      params,
      responseType: 'blob'
    })
  },

  // ==================== 下拉数据接口 ====================

  // 获取订单下拉数据（商机、线索、客户、负责人等）
  getDropdownData: async (params: GetDropdownDataParams): Promise<DropdownDataResult> => {
    return await request.post({ url: '/publicbiz/order/dropdown-data', data: params })
  },

  // ==================== 商机线索下沉接口 ====================

  // 商机状态下沉更新
  sinkBusinessOpportunity: async (params: {
    opportunityId: number
    orderId: number
    orderNo: string
    status: string
    remark?: string
  }): Promise<{ success: boolean; message: string }> => {
    return await request.post({ url: '/publicbiz/opportunity/sink-status', data: params })
  },

  // 线索状态下沉更新
  sinkLead: async (params: {
    leadId: number
    orderId: number
    orderNo: string
    status: string
    remark?: string
  }): Promise<{ success: boolean; message: string }> => {
    return await request.post({ url: '/publicbiz/lead/sink-status', data: params })
  }
}
